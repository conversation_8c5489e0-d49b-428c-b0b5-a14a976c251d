//
//  ThemeManager.swift
//  Appio
//
//  Created by gondo on 17/09/2025.
//

import SwiftUI

/// ThemeManager provides computed colors based on a ServiceEntity's theme configuration
struct ThemeManager {
    private let service: ServiceEntity?

    init(service: ServiceEntity?) {
        self.service = service
    }

    // MARK: - Default Colors (fallbacks when service colors are nil)

    private struct DefaultColors {
        static let primary: Color = .primary
        static let secondary: Color = .secondary
        static let accent: Color = .accentColor
        static let background: Color = Color(.systemGroupedBackground)
    }

    // MARK: - Computed Theme Colors

    /// Primary text color - uses service textColor if available, otherwise default
    func primaryText(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.primary
        }

        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.primary)
    }

    /// Secondary text color - derived from primary text or uses default
    func secondaryText(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.secondary
        }

        // Use the service text color with reduced opacity for secondary text
        let serviceColor = textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.primary)
        return serviceColor.opacity(0.7)
    }

    /// Primary background color - uses service backgroundColor if available, otherwise default
    func primaryBackground(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let backgroundColorString = service.backgroundColor,
              !backgroundColorString.isEmpty else {
            return DefaultColors.background
        }

        return backgroundColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.background)
    }

    /// Accent color - uses service textColor as accent, or default accent
    func accent(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.accent
        }

        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.accent)
    }
}

// MARK: - Static Factory Methods

extension ThemeManager {
    /// Creates a ThemeManager with default colors (no service)
    static var `default`: ThemeManager {
        return ThemeManager(service: nil)
    }
    
    /// Creates a ThemeManager for a specific service
    static func for(service: ServiceEntity) -> ThemeManager {
        return ThemeManager(service: service)
    }
}
