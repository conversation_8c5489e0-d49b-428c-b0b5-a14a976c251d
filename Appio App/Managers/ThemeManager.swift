//
//  ThemeManager.swift
//  Appio
//
//  Created by gondo on 17/09/2025.
//

import SwiftUI

/// ThemeManager provides computed colors based on a ServiceEntity's theme configuration
struct ThemeManager {
    private let service: ServiceEntity?
    
    init(service: ServiceEntity?) {
        self.service = service
    }
    
    // MARK: - Default Colors (fallbacks when service colors are nil)
    
    private struct DefaultColors {
        static let primary: Color = .primary
        static let secondary: Color = .secondary
        static let accent: Color = .accentColor
        static let background: Color = Color(.systemGroupedBackground)
        static let cardBackground: Color = Color(.secondarySystemGroupedBackground)
        static let text: Color = .primary
        static let secondaryText: Color = .secondary
        static let separator: Color = Color(.separator)
    }
    
    // MARK: - Computed Theme Colors
    
    /// Primary text color - uses service textColor if available, otherwise default
    func primaryText(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.text
        }
        
        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.text)
    }
    
    /// Secondary text color - derived from primary text or uses default
    func secondaryText(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.secondaryText
        }
        
        // Use the service text color with reduced opacity for secondary text
        let serviceColor = textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.text)
        return serviceColor.opacity(0.7)
    }
    
    /// Primary background color - uses service backgroundColor if available, otherwise default
    func primaryBackground(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let backgroundColorString = service.backgroundColor,
              !backgroundColorString.isEmpty else {
            return DefaultColors.background
        }
        
        return backgroundColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.background)
    }
    
    /// Card/secondary background color - derived from primary background or uses default
    func cardBackground(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let backgroundColorString = service.backgroundColor,
              !backgroundColorString.isEmpty else {
            return DefaultColors.cardBackground
        }
        
        // Create a slightly different shade for cards
        let serviceBackground = backgroundColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.background)
        
        // For light mode, make it slightly lighter; for dark mode, slightly darker
        if colorScheme == .dark {
            return serviceBackground.opacity(0.8)
        } else {
            return Color.white.opacity(0.9).blendMode(.overlay)
        }
    }
    
    /// Accent color - uses service textColor as accent, or default accent
    func accent(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.accent
        }
        
        return textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.accent)
    }
    
    /// Separator color - derived from text color or uses default
    func separator(colorScheme: ColorScheme) -> Color {
        guard let service = service,
              let textColorString = service.textColor,
              !textColorString.isEmpty else {
            return DefaultColors.separator
        }
        
        let serviceColor = textColorString.toColor(colorScheme: colorScheme, fallback: DefaultColors.text)
        return serviceColor.opacity(0.2)
    }
    
    /// Button background color - uses accent color
    func buttonBackground(colorScheme: ColorScheme) -> Color {
        return accent(colorScheme: colorScheme)
    }
    
    /// Button text color - contrasts with button background
    func buttonText(colorScheme: ColorScheme) -> Color {
        // For now, use white text on colored buttons
        // Could be enhanced to calculate contrast automatically
        return .white
    }
    
    // MARK: - Convenience Methods
    
    /// Returns true if the service has custom theme colors configured
    var hasCustomTheme: Bool {
        guard let service = service else { return false }
        
        let hasTextColor = service.textColor != nil && !service.textColor!.isEmpty
        let hasBackgroundColor = service.backgroundColor != nil && !service.backgroundColor!.isEmpty
        
        return hasTextColor || hasBackgroundColor
    }
    
    /// Returns the service title for debugging/logging purposes
    var serviceTitle: String {
        return service?.title ?? "No Service"
    }
}

// MARK: - Static Factory Methods

extension ThemeManager {
    /// Creates a ThemeManager with default colors (no service)
    static var `default`: ThemeManager {
        return ThemeManager(service: nil)
    }
    
    /// Creates a ThemeManager for a specific service
    static func for(service: ServiceEntity) -> ThemeManager {
        return ThemeManager(service: service)
    }
}
